#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
全自动绘本生成脚本演示版本
使用现有的图片文件演示完整流程，跳过豆包网站图片生成步骤
"""

import os
import sys
import asyncio
import argparse
import subprocess
import shutil
from pathlib import Path
from datetime import datetime


class HuibenGeneratorDemo:
    """绘本生成器演示版本"""
    
    def __init__(self, huiben_file="huiben.md"):
        self.huiben_file = huiben_file
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 定义各阶段的目录
        self.source_images_dir = "images"  # 源图片目录（演示用）
        self.doubao_output_dir = "test_images"  # 豆包生成图片输出目录
        self.watermark_input_dir = "test_images"  # 水印去除输入目录
        self.watermark_output_dir = "output"  # 水印去除输出目录
        self.final_output_dir = "output_images"  # 最终绘本输出目录
        
        # 水印相关配置
        self.mask_path = "mask/mask.jpg"
        
        # 确保必要目录存在
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保所有必要的目录存在"""
        directories = [
            self.doubao_output_dir,
            self.watermark_output_dir, 
            self.final_output_dir,
            "mask"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            print(f"✓ 确保目录存在: {directory}")
    
    def _check_huiben_file(self):
        """检查huiben.md文件是否存在"""
        if not os.path.exists(self.huiben_file):
            print(f"❌ 错误: 找不到绘本文件 '{self.huiben_file}'")
            return False
        
        print(f"✓ 找到绘本文件: {self.huiben_file}")
        return True
    
    def _check_mask_file(self):
        """检查水印蒙版文件是否存在"""
        if not os.path.exists(self.mask_path):
            print(f"❌ 错误: 找不到水印蒙版文件 '{self.mask_path}'")
            print("请确保mask目录下有mask.jpg文件")
            return False
        
        print(f"✓ 找到水印蒙版文件: {self.mask_path}")
        return True
    
    def _check_source_images(self):
        """检查源图片目录是否有图片"""
        if not os.path.exists(self.source_images_dir):
            print(f"❌ 错误: 找不到源图片目录 '{self.source_images_dir}'")
            return False
        
        image_count = self._count_generated_images(self.source_images_dir)
        if image_count == 0:
            print(f"❌ 错误: 源图片目录 '{self.source_images_dir}' 中没有图片")
            return False
        
        print(f"✓ 找到源图片目录，包含 {image_count} 张图片")
        return True
    
    def _count_generated_images(self, directory):
        """统计生成的图片数量"""
        if not os.path.exists(directory):
            return 0
        
        image_extensions = ['.png', '.jpg', '.jpeg']
        count = 0
        
        for file in os.listdir(directory):
            if any(file.lower().endswith(ext) for ext in image_extensions):
                count += 1
        
        return count
    
    def step1_copy_demo_images(self):
        """步骤1: 复制演示图片（模拟豆包生成）"""
        print("\n" + "="*50)
        print("步骤1: 复制演示图片（模拟豆包生成）")
        print("="*50)
        
        try:
            # 清理之前的输出
            if os.path.exists(self.doubao_output_dir):
                shutil.rmtree(self.doubao_output_dir)
                print(f"清理旧的输出目录: {self.doubao_output_dir}")
            
            # 创建输出目录
            os.makedirs(self.doubao_output_dir, exist_ok=True)
            
            # 复制图片文件
            copied_count = 0
            for i in range(1, 10):
                source_file = os.path.join(self.source_images_dir, f"{i}.png")
                if os.path.exists(source_file):
                    target_file = os.path.join(self.doubao_output_dir, f"{i}.png")
                    shutil.copy2(source_file, target_file)
                    print(f"✓ 复制图片: {source_file} -> {target_file}")
                    copied_count += 1
                else:
                    print(f"⚠️  跳过不存在的图片: {source_file}")
            
            if copied_count > 0:
                print(f"✓ 成功复制 {copied_count} 张演示图片")
                return True
            else:
                print("❌ 没有复制任何图片")
                return False
                
        except Exception as e:
            print(f"❌ 复制演示图片过程出错: {e}")
            return False
    
    def step2_remove_watermarks(self):
        """步骤2: 去除图片水印"""
        print("\n" + "="*50)
        print("步骤2: 去除图片水印")
        print("="*50)
        
        try:
            # 检查输入图片是否存在
            input_count = self._count_generated_images(self.watermark_input_dir)
            if input_count == 0:
                print(f"❌ 错误: 在 '{self.watermark_input_dir}' 目录中没有找到图片")
                return False
            
            print(f"找到 {input_count} 张图片需要去除水印")
            
            # 清理之前的输出
            if os.path.exists(self.watermark_output_dir):
                shutil.rmtree(self.watermark_output_dir)
                print(f"清理旧的输出目录: {self.watermark_output_dir}")
            
            # 运行水印去除脚本
            print("正在去除水印...")
            
            cmd = [
                sys.executable, "remove_watermark_precise.py",
                "-i", self.watermark_input_dir,
                "-o", self.watermark_output_dir,
                "-m", self.mask_path,
                "--debug"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✓ 水印去除完成")
                print(result.stdout)
                
                # 检查处理后的图片数量
                output_count = self._count_generated_images(self.watermark_output_dir)
                print(f"✓ 成功处理 {output_count} 张图片")
                
                return output_count > 0
            else:
                print("❌ 水印去除失败")
                print("错误输出:", result.stderr)
                return False
                
        except Exception as e:
            print(f"❌ 水印去除过程出错: {e}")
            return False
    
    async def step3_generate_final_huiben(self):
        """步骤3: 生成最终绘本图片"""
        print("\n" + "="*50)
        print("步骤3: 生成最终绘本图片")
        print("="*50)
        
        try:
            # 检查去水印后的图片是否存在
            input_count = self._count_generated_images(self.watermark_output_dir)
            if input_count == 0:
                print(f"❌ 错误: 在 '{self.watermark_output_dir}' 目录中没有找到处理后的图片")
                return False
            
            print(f"找到 {input_count} 张去水印后的图片")
            
            # 清理之前的输出
            if os.path.exists(self.final_output_dir):
                shutil.rmtree(self.final_output_dir)
                print(f"清理旧的输出目录: {self.final_output_dir}")
            
            # 运行绘本生成脚本
            print("正在生成最终绘本图片...")
            
            process = await asyncio.create_subprocess_exec(
                sys.executable, "generate_images.py",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                print("✓ 绘本图片生成完成")
                print(stdout.decode('utf-8', errors='ignore'))
                
                # 检查最终生成的图片数量
                final_count = self._count_generated_images(self.final_output_dir)
                print(f"✓ 成功生成 {final_count} 张最终绘本图片")
                
                return final_count > 0
            else:
                print("❌ 绘本图片生成失败")
                print("错误输出:", stderr.decode('utf-8', errors='ignore'))
                return False
                
        except Exception as e:
            print(f"❌ 绘本图片生成过程出错: {e}")
            return False
    
    async def run_demo_pipeline(self):
        """运行演示版绘本生成流程"""
        print("🎬 开始演示版全自动绘本生成流程")
        print(f"📖 使用绘本文件: {self.huiben_file}")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("📝 注意: 这是演示版本，使用现有图片跳过豆包网站生成步骤")
        
        # 预检查
        if not self._check_huiben_file():
            return False
        
        if not self._check_mask_file():
            return False
        
        if not self._check_source_images():
            return False
        
        # 执行三个步骤
        success = True
        
        # 步骤1: 复制演示图片
        if success:
            success = self.step1_copy_demo_images()
        
        # 步骤2: 去除水印
        if success:
            success = self.step2_remove_watermarks()
        
        # 步骤3: 生成最终绘本
        if success:
            success = await self.step3_generate_final_huiben()
        
        # 总结
        print("\n" + "="*50)
        if success:
            print("🎉 演示版全自动绘本生成流程完成!")
            print(f"📁 最终图片保存在: {self.final_output_dir}")
            print("✨ 您的演示绘本已经准备好了!")
            print("\n📋 流程总结:")
            print(f"  1. 源图片: {self.source_images_dir}")
            print(f"  2. 模拟豆包输出: {self.doubao_output_dir}")
            print(f"  3. 去水印后: {self.watermark_output_dir}")
            print(f"  4. 最终绘本: {self.final_output_dir}")
        else:
            print("❌ 演示版绘本生成流程失败")
            print("请检查上述错误信息并重试")
        
        print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*50)
        
        return success


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="全自动绘本生成脚本演示版")
    
    parser.add_argument('-f', '--file', type=str, default='huiben.md',
                      help='绘本文件路径 (默认: huiben.md)')
    
    return parser.parse_args()


async def main():
    """主函数"""
    args = parse_args()
    
    # 创建绘本生成器实例
    generator = HuibenGeneratorDemo(huiben_file=args.file)
    
    # 运行演示流程
    success = await generator.run_demo_pipeline()
    
    # 退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())
