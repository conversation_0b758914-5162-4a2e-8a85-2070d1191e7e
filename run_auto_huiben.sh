#!/bin/bash

# 全自动绘本生成启动脚本
# 使用方法: ./run_auto_huiben.sh [huiben.md文件路径]

echo "🚀 启动全自动绘本生成流程..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3，请先安装Python 3.7+"
    exit 1
fi

# 检查虚拟环境
if [ -d "venv" ]; then
    echo "✓ 发现虚拟环境，正在激活..."
    source venv/bin/activate
else
    echo "⚠️  警告: 未发现虚拟环境，使用系统Python"
fi

# 检查依赖
echo "📦 检查依赖..."
if ! python3 -c "import playwright" &> /dev/null; then
    echo "❌ 错误: 未安装playwright，请运行: pip install playwright"
    exit 1
fi

if ! python3 -c "import jinja2" &> /dev/null; then
    echo "❌ 错误: 未安装jinja2，请运行: pip install jinja2"
    exit 1
fi

# 检查huiben.md文件
HUIBEN_FILE=${1:-"huiben.md"}
if [ ! -f "$HUIBEN_FILE" ]; then
    echo "❌ 错误: 找不到绘本文件 '$HUIBEN_FILE'"
    echo "请确保文件存在，或指定正确的文件路径"
    echo "使用方法: $0 [huiben.md文件路径]"
    exit 1
fi

echo "✓ 使用绘本文件: $HUIBEN_FILE"

# 检查必要文件
if [ ! -f "auto_generate_huiben.py" ]; then
    echo "❌ 错误: 找不到主脚本 auto_generate_huiben.py"
    exit 1
fi

if [ ! -f "doubao_simple_test.py" ]; then
    echo "❌ 错误: 找不到豆包生成脚本 doubao_simple_test.py"
    exit 1
fi

if [ ! -f "remove_watermark_precise.py" ]; then
    echo "❌ 错误: 找不到水印去除脚本 remove_watermark_precise.py"
    exit 1
fi

if [ ! -f "generate_images.py" ]; then
    echo "❌ 错误: 找不到绘本生成脚本 generate_images.py"
    exit 1
fi

# 检查水印蒙版文件
if [ ! -f "mask/mask.jpg" ]; then
    echo "❌ 错误: 找不到水印蒙版文件 mask/mask.jpg"
    echo "请确保mask目录下有mask.jpg文件"
    exit 1
fi

echo "✅ 所有检查通过，开始执行..."
echo ""

# 运行主脚本
python3 auto_generate_huiben.py --file "$HUIBEN_FILE"

# 检查执行结果
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 全自动绘本生成完成！"
    echo "📁 请查看 output_images 目录中的最终结果"
else
    echo ""
    echo "❌ 绘本生成过程中出现错误"
    echo "请查看上述错误信息并重试"
    exit 1
fi
