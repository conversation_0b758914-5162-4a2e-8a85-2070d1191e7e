<!DOCTYPE html>
<html>
<head>
    <title>Break Button Detection Test</title>
    <style>
        .hidden { display: none; }
        .!hidden { display: block; }
        button { padding: 10px; margin: 10px; }
    </style>
</head>
<body>
    <h1>Break Button Detection Test</h1>
    
    <!-- 测试场景1: 按钮包含!hidden类（生成完成） -->
    <div>
        <h2>场景1: 生成完成（包含!hidden）</h2>
        <button data-testid="chat_input_local_break_button" class="btn !hidden">
            Break Button (Complete)
        </button>
    </div>
    
    <!-- 测试场景2: 按钮不包含!hidden类（生成中） -->
    <div>
        <h2>场景2: 生成中（不包含!hidden）</h2>
        <button data-testid="chat_input_local_break_button" class="btn hidden">
            Break Button (Generating)
        </button>
    </div>
    
    <!-- 测试场景3: 按钮不存在 -->
    <div>
        <h2>场景3: 按钮不存在</h2>
        <p>没有break按钮</p>
    </div>
    
    <script>
        // 测试检测逻辑
        function testDetection() {
            const breakButton = document.querySelector('[data-testid="chat_input_local_break_button"]');
            if (!breakButton) {
                console.log("未找到break按钮");
                return { found: false, status: "no_break_button" };
            }

            const classList = breakButton.className || '';
            const hasNotHidden = classList.includes('!hidden');
            
            console.log("Break按钮class:", classList);
            console.log("包含!hidden:", hasNotHidden);

            if (hasNotHidden) {
                console.log("检测到图像生成完成（break按钮包含!hidden）");
                return { 
                    found: true, 
                    status: "generation_complete",
                    buttonClass: classList
                };
            } else {
                console.log("图像仍在生成中（break按钮不包含!hidden）");
                return { 
                    found: false, 
                    status: "generating",
                    buttonClass: classList
                };
            }
        }
        
        // 页面加载后运行测试
        window.onload = function() {
            console.log("开始测试检测逻辑...");
            const result = testDetection();
            console.log("检测结果:", result);
        };
    </script>
</body>
</html>