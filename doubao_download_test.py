#!/usr/bin/env python3
"""
豆包图片下载功能专项测试脚本
基于提供的HTML结构信息进行精确测试
"""

import asyncio
import os
import json
from datetime import datetime
from playwright.async_api import async_playwright

# 配置
OUTPUT_DIR = "test_images"
AUTH_STATE_FILE = "doubao_auth_state.json"
TEST_URL = "https://www.doubao.com/chat/7340111199249410"

async def main():
    print("🧪 豆包图片下载功能专项测试...")

    # 确保输出目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    async with async_playwright() as p:
        # 启动浏览器
        print("🚀 启动Chrome浏览器...")
        browser = await p.chromium.launch(
            channel='chrome',  # 使用系统安装的Chrome
            headless=False,
            args=[
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=IsolateOrigins,site-per-process'
            ]
        )

        # 创建上下文并设置下载路径
        download_path = os.path.abspath(OUTPUT_DIR)
        context = await browser.new_context(
            # viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            accept_downloads=True
        )

        # 加载登录状态
        if os.path.exists(AUTH_STATE_FILE):
            print(f"📂 加载保存的登录状态: {AUTH_STATE_FILE}")
            with open(AUTH_STATE_FILE, 'r') as f:
                auth_state = json.load(f)

            # 设置cookies
            if 'cookies' in auth_state:
                await context.add_cookies(auth_state['cookies'])
                print(f"✅ 成功加载 {len(auth_state['cookies'])} 个cookies")

        # 创建页面
        page = await context.new_page()

        # 设置下载路径
        await page.set_extra_http_headers({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        })

        try:
            # 访问测试页面
            print(f"🌐 访问测试页面: {TEST_URL}")
            response = await page.goto(TEST_URL, wait_until='networkidle', timeout=30000)
            print(f"✅ 页面加载成功，状态码: {response.status}")

            # 等待页面完全加载
            await asyncio.sleep(5)

            # 保存页面截图
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            await page.screenshot(path=f"{OUTPUT_DIR}/test_page_{timestamp}.png")
            print(f"📸 已保存页面截图")

            # 查找所有图片容器
            print("🔍 查找页面中的所有图片容器...")
            containers_info = await page.evaluate("""() => {
                const containers = [];
                const imageBoxes = document.querySelectorAll('[class*="image-box-grid-item"]');
                console.log(`找到 ${imageBoxes.length} 个图片容器`);

                imageBoxes.forEach((box, index) => {
                    containers.push({
                        index: index,
                        className: box.className
                    });
                });

                return containers;
            }""")

            print(f"📊 找到 {len(containers_info)} 个图片容器")

            # 直接下载所有图片
            print("🔄 开始下载所有图片...")
            download_count = 0

            # 查找所有下载按钮
            print("🔍 查找所有下载按钮...")
            all_buttons = await page.query_selector_all('[data-testid="edit_image_hover_tag_download_btn"]')
            print(f"📊 找到 {len(all_buttons)} 个下载按钮")

            if len(all_buttons) == 0:
                print("❌ 没有找到任何下载按钮")
                return

            # 强制显示所有下载按钮
            print("🔧 强制显示所有下载按钮...")
            await page.evaluate("""() => {
                const buttons = document.querySelectorAll('[data-testid="edit_image_hover_tag_download_btn"]');
                buttons.forEach(btn => {
                    // 强制显示按钮
                    btn.style.display = 'block';
                    btn.style.visibility = 'visible';
                    btn.style.opacity = '1';

                    // 移除可能的隐藏类
                    btn.classList.remove('hidden');

                    // 确保父元素也可见
                    let parent = btn.parentElement;
                    while (parent) {
                        parent.style.display = 'block';
                        parent.style.visibility = 'visible';
                        parent.style.opacity = '1';
                        parent = parent.parentElement;
                    }
                });

                console.log(`强制显示了 ${buttons.length} 个下载按钮`);
            }""")

            # 等待一下让样式生效
            await asyncio.sleep(2)

            # 重新获取按钮列表
            all_buttons = await page.query_selector_all('[data-testid="edit_image_hover_tag_download_btn"]')
            print(f"🔄 重新获取到 {len(all_buttons)} 个下载按钮")

            # 逐个点击下载按钮并保存文件
            for i, button in enumerate(all_buttons):
                print(f"\n🖼️ 处理第 {i+1} 个下载按钮...")

                try:
                    # 检查按钮状态
                    is_visible = await button.is_visible()
                    is_enabled = await button.is_enabled()
                    print(f"   按钮状态: 可见={is_visible}, 可用={is_enabled}")

                    # 设置下载监听并点击
                    try:
                        # 监听下载事件
                        async with page.expect_download(timeout=15000) as download_info:
                            await button.click(force=True)
                            print("✅ 已点击下载按钮，等待下载...")

                        # 获取下载对象
                        download = await download_info.value
                        print(f"📥 检测到下载: {download.suggested_filename}")

                        # 保存到指定文件夹
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        filename = f"image_{i+1}_{timestamp}_{download.suggested_filename}"
                        save_path = os.path.join(download_path, filename)

                        await download.save_as(save_path)
                        print(f"💾 已保存到: {save_path}")
                        download_count += 1

                    except Exception as download_error:
                        print(f"❌ 下载失败: {download_error}")
                        # 如果下载监听失败，仍然点击按钮（可能会下载到默认文件夹）
                        try:
                            await button.click(force=True)
                            print("✅ 已点击下载按钮（可能下载到默认文件夹）")
                        except Exception as click_error:
                            print(f"❌ 点击失败: {click_error}")

                except Exception as e:
                    print(f"❌ 处理第 {i+1} 个按钮失败: {e}")

                # 等待一下，避免操作过快
                await asyncio.sleep(2)

            print(f"\n🎉 下载完成！成功下载了 {download_count} 张图片")

        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            await page.screenshot(path=f"{OUTPUT_DIR}/error_{timestamp}.png")

        finally:
            # 等待用户查看结果
            print("⏳ 等待30秒让用户查看结果...")
            await asyncio.sleep(30)

            # 关闭浏览器
            await browser.close()

if __name__ == "__main__":
    asyncio.run(main())
