# 豆包网站图片生成脚本执行流程分析

## 脚本概述
这是一个使用 Playwright 自动化测试豆包网站图片生成功能的 Python 脚本。脚本实现了完整的自动化流程，包括登录状态管理、页面导航、输入处理和图片下载。

## 主要依赖库
- `playwright.async_api`: 浏览器自动化
- `pyperclip`: 剪贴板操作（可选）
- `asyncio`: 异步编程
- `json`: 登录状态存储
- `datetime`: 时间戳生成

## 核心配置参数
```python
OUTPUT_DIR = "test_images"           # 截图输出目录
AUTH_FILE = "doubao_auth_state.json" # 登录状态保存文件
LONG_TIMEOUT = 120000               # 长超时时间（120秒）
STANDARD_TIMEOUT = 60000            # 标准超时时间（60秒）
SHORT_TIMEOUT = 30000               # 短超时时间（30秒）
MAX_RETRIES = 3                     # 最大重试次数
```

## 详细执行流程

### 1. 初始化阶段
- **创建输出目录**: 创建 `test_images` 目录存储截图
- **检查依赖**: 检测 `pyperclip` 库是否可用
- **浏览器启动**: 启动 Chrome 浏览器（非无头模式）
  - 使用系统安装的 Chrome
  - 配置安全参数和用户代理

### 2. 登录状态管理
- **检查保存的状态**: 查找 `doubao_auth_state.json` 文件
- **状态加载**: 
  - 如果存在状态文件，尝试加载 cookies 和存储状态
  - 如果加载失败，创建新会话
- **新会话创建**: 如果没有保存状态，创建全新的浏览器上下文

### 3. 网站访问
- **访问豆包**: 导航到 `https://www.doubao.com/chat/`
- **重试机制**: 最多重试3次，每次失败后等待5秒
- **页面加载等待**: 
  - 等待 DOM 内容加载
  - 等待页面资源加载
  - 等待网络活动停止
- **截图记录**: 保存页面加载完成后的截图

### 4. 登录检测与处理
- **登录状态检测**: 
  - 检查已登录指标（用户头像、用户信息等）
  - 检查登录按钮存在性
- **手动登录等待**: 
  - 如果需要登录，等待30秒供用户手动登录
  - 保存登录前后的截图
- **状态保存**: 登录成功后保存浏览器状态到 JSON 文件

### 5. 图像生成入口查找
- **多选择器策略**: 使用多种 CSS 选择器查找图像生成入口
  ```python
  image_selectors = [
      "div[title='图像生成']",
      ".section-item-title-BjpNe2", 
      "div.title-IK319y[title='图像生成']",
      "div:has-text('图像生成')",
      "text=图像生成", 
      # ... 更多选择器
  ]
  ```
- **渐进式查找**: 先用短超时快速检查，再用长超时深度查找
- **点击操作**: 
  - 优先使用 JavaScript 点击
  - 备选使用 Playwright 原生点击

### 6. 输入框处理
- **输入框定位**: 使用多种选择器查找输入框
  ```python
  input_selectors = [
      "[data-slate-editor='true']",
      "[data-testid='chat_input_input']", 
      "[role='textbox']",
      # ... 更多选择器
  ]
  ```
- **测试提示词**: 预设的童书插画描述文本

### 7. 文本输入策略（多重备选方案）
#### 方案1: 系统剪贴板 + 键盘快捷键
- 使用 `pyperclip` 复制文本到系统剪贴板
- 根据操作系统使用相应快捷键（macOS: Cmd+V, 其他: Ctrl+V）

#### 方案2: execCommand API
- 创建临时 textarea 元素
- 使用 `document.execCommand('copy')` 和 `document.execCommand('paste')`

#### 方案3: 直接DOM操作
- 直接设置 Slate 编辑器的文本内容
- 触发必要的 DOM 事件

### 8. 发送按钮激活
- **按钮状态修改**: 
  - 移除 `disabled` 属性
  - 修改 CSS 类名
  - 触发点击事件
- **多重点击策略**: JavaScript 点击 + 原生事件触发

### 9. 图像生成监控
- **循环检测**: 每5秒检查一次，最长等待2分钟
- **多重检测策略**:
  - 检测图像元素（`img` 标签）
  - 检测下载按钮
  - 检测生成完成标志
- **定期截图**: 每15秒保存一次等待状态截图

### 10. 结果处理
#### 图像保存
- **直接截图**: 对找到的图像元素进行截图
- **下载处理**: 
  - 查找下载按钮
  - 使用 Playwright 的下载 API
  - 保存到指定路径

#### 备选方案
- **页面截图**: 如果下载失败，保存整个页面截图
- **最大图像**: 查找页面中最大的图像元素并截图

### 11. 错误处理与恢复
- **超时处理**: 每个操作都有超时和重试机制
- **错误截图**: 发生错误时自动保存状态截图
- **资源清理**: 
  - 恢复原始剪贴板内容
  - 关闭浏览器实例

## 关键技术特点

### 1. 健壮性设计
- 多重重试机制
- 渐进式超时策略
- 多种备选方案

### 2. 状态管理
- 登录状态持久化
- 浏览器上下文复用
- 错误状态记录

### 3. 兼容性处理
- 多种选择器策略
- 跨平台键盘快捷键
- 不同输入方法适配

### 4. 调试友好
- 详细的日志输出
- 关键节点截图
- 错误状态保存

## 输出文件说明
- `doubao_loaded_*.png`: 页面加载完成截图
- `before_login_*.png`: 登录前状态
- `after_login_*.png`: 登录后状态
- `before_click_image_*.png`: 点击图像生成前
- `image_page_*.png`: 图像生成页面
- `input_complete_*.png`: 输入完成状态
- `waiting_for_image_*.png`: 等待生成过程
- `generation_result_*.png`: 生成结果
- `final_state_*.png`: 最终状态

## 使用建议
1. 确保系统已安装 Chrome 浏览器
2. 首次运行需要手动完成登录
3. 网络环境要稳定，避免超时
4. 定期清理输出目录中的截图文件
