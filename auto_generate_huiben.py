#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
全自动绘本生成脚本
整合豆包图片生成、水印去除、绘本模板生成的完整流程

主要流程：
1. 使用豆包AI生成图片 (doubao_simple_test.py)
2. 去除图片水印 (remove_watermark_precise.py)
3. 生成最终绘本图片 (generate_images.py)
"""

import os
import sys
import asyncio
import argparse
import subprocess
import shutil
from pathlib import Path
from datetime import datetime


class HuibenGenerator:
    """绘本生成器主类"""

    def __init__(self, huiben_file="huiben.md"):
        self.huiben_file = huiben_file
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 定义各阶段的目录
        self.doubao_output_dir = "test_images"  # 豆包生成图片输出目录
        self.watermark_input_dir = "test_images"  # 水印去除输入目录
        self.watermark_output_dir = "output"  # 水印去除输出目录
        self.final_output_dir = "output_images"  # 最终绘本输出目录

        # 水印相关配置
        self.mask_path = "mask/mask.jpg"

        # 确保必要目录存在
        self._ensure_directories()

    def _ensure_directories(self):
        """确保所有必要的目录存在"""
        directories = [
            self.doubao_output_dir,
            self.watermark_output_dir,
            self.final_output_dir,
            "mask"
        ]

        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            print(f"✓ 确保目录存在: {directory}")

    def _check_huiben_file(self):
        """检查huiben.md文件是否存在"""
        if not os.path.exists(self.huiben_file):
            print(f"❌ 错误: 找不到绘本文件 '{self.huiben_file}'")
            return False

        print(f"✓ 找到绘本文件: {self.huiben_file}")
        return True

    def _check_mask_file(self):
        """检查水印蒙版文件是否存在"""
        if not os.path.exists(self.mask_path):
            print(f"❌ 错误: 找不到水印蒙版文件 '{self.mask_path}'")
            print("请确保mask目录下有mask.jpg文件")
            return False

        print(f"✓ 找到水印蒙版文件: {self.mask_path}")
        return True

    async def step1_generate_images_with_doubao(self):
        """步骤1: 使用豆包AI生成图片"""
        print("\n" + "="*50)
        print("步骤1: 使用豆包AI生成图片")
        print("="*50)

        try:
            # 清理之前的输出
            if os.path.exists(self.doubao_output_dir):
                shutil.rmtree(self.doubao_output_dir)
                print(f"清理旧的输出目录: {self.doubao_output_dir}")

            # 运行豆包图片生成脚本
            print("正在启动豆包图片生成...")
            print("注意: 如果需要登录，请在浏览器中完成登录操作")

            # 使用subprocess运行doubao_simple_test.py
            process = await asyncio.create_subprocess_exec(
                sys.executable, "doubao_simple_test.py",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                print("✓ 豆包图片生成完成")
                print(stdout.decode('utf-8', errors='ignore'))

                # 检查生成的图片数量
                generated_count = self._count_generated_images(self.doubao_output_dir)
                print(f"✓ 成功生成 {generated_count} 张图片")

                if generated_count == 0:
                    print("❌ 警告: 没有生成任何图片，请检查豆包脚本执行情况")
                    return False

                # 重命名下载的图片为标准格式
                print("正在重命名图片为标准格式...")
                if self._rename_downloaded_images(self.doubao_output_dir):
                    print("✓ 图片重命名完成")
                else:
                    print("❌ 图片重命名失败，但继续执行")

                return True
            else:
                print("❌ 豆包图片生成失败")
                print("错误输出:", stderr.decode('utf-8', errors='ignore'))
                return False

        except Exception as e:
            print(f"❌ 豆包图片生成过程出错: {e}")
            return False

    def _count_generated_images(self, directory):
        """统计生成的图片数量"""
        if not os.path.exists(directory):
            return 0

        image_extensions = ['.png', '.jpg', '.jpeg']
        count = 0

        for file in os.listdir(directory):
            if any(file.lower().endswith(ext) for ext in image_extensions):
                count += 1

        return count

    def _rename_downloaded_images(self, source_dir):
        """将下载的图片重命名为标准格式 (1.png, 2.png, ...)"""
        if not os.path.exists(source_dir):
            print(f"❌ 源目录不存在: {source_dir}")
            return False

        # 获取所有图片文件
        image_extensions = ['.png', '.jpg', '.jpeg']
        image_files = []

        for file in os.listdir(source_dir):
            if any(file.lower().endswith(ext) for ext in image_extensions):
                # 排除截图文件
                if not any(keyword in file.lower() for keyword in ['screenshot', 'error', 'login', 'loaded', 'final']):
                    image_files.append(file)

        # 按文件名排序，确保顺序正确
        image_files.sort()

        print(f"找到 {len(image_files)} 张需要重命名的图片")

        # 重命名文件
        renamed_count = 0
        for i, old_filename in enumerate(image_files, 1):
            old_path = os.path.join(source_dir, old_filename)

            # 获取文件扩展名
            _, ext = os.path.splitext(old_filename)
            if not ext:
                ext = '.png'  # 默认扩展名

            # 新文件名
            new_filename = f"{i}{ext}"
            new_path = os.path.join(source_dir, new_filename)

            try:
                # 如果目标文件已存在，先删除
                if os.path.exists(new_path):
                    os.remove(new_path)

                # 重命名文件
                os.rename(old_path, new_path)
                print(f"✓ 重命名: {old_filename} -> {new_filename}")
                renamed_count += 1

            except Exception as e:
                print(f"❌ 重命名失败: {old_filename} -> {new_filename}, 错误: {e}")

        print(f"✓ 成功重命名 {renamed_count} 张图片")
        return renamed_count > 0

    def step2_remove_watermarks(self):
        """步骤2: 去除图片水印"""
        print("\n" + "="*50)
        print("步骤2: 去除图片水印")
        print("="*50)

        try:
            # 检查输入图片是否存在
            input_count = self._count_generated_images(self.watermark_input_dir)
            if input_count == 0:
                print(f"❌ 错误: 在 '{self.watermark_input_dir}' 目录中没有找到图片")
                return False

            print(f"找到 {input_count} 张图片需要去除水印")

            # 清理之前的输出
            if os.path.exists(self.watermark_output_dir):
                shutil.rmtree(self.watermark_output_dir)
                print(f"清理旧的输出目录: {self.watermark_output_dir}")

            # 运行水印去除脚本
            print("正在去除水印...")

            cmd = [
                sys.executable, "remove_watermark_precise.py",
                "-i", self.watermark_input_dir,
                "-o", self.watermark_output_dir,
                "-m", self.mask_path,
                "--debug"
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                print("✓ 水印去除完成")
                print(result.stdout)

                # 检查处理后的图片数量
                output_count = self._count_generated_images(self.watermark_output_dir)
                print(f"✓ 成功处理 {output_count} 张图片")

                return output_count > 0
            else:
                print("❌ 水印去除失败")
                print("错误输出:", result.stderr)
                return False

        except Exception as e:
            print(f"❌ 水印去除过程出错: {e}")
            return False

    async def step3_generate_final_huiben(self):
        """步骤3: 生成最终绘本图片"""
        print("\n" + "="*50)
        print("步骤3: 生成最终绘本图片")
        print("="*50)

        try:
            # 检查去水印后的图片是否存在
            input_count = self._count_generated_images(self.watermark_output_dir)
            if input_count == 0:
                print(f"❌ 错误: 在 '{self.watermark_output_dir}' 目录中没有找到处理后的图片")
                return False

            print(f"找到 {input_count} 张去水印后的图片")

            # 清理之前的输出
            if os.path.exists(self.final_output_dir):
                shutil.rmtree(self.final_output_dir)
                print(f"清理旧的输出目录: {self.final_output_dir}")

            # 运行绘本生成脚本
            print("正在生成最终绘本图片...")

            process = await asyncio.create_subprocess_exec(
                sys.executable, "generate_images.py",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                print("✓ 绘本图片生成完成")
                print(stdout.decode('utf-8', errors='ignore'))

                # 检查最终生成的图片数量
                final_count = self._count_generated_images(self.final_output_dir)
                print(f"✓ 成功生成 {final_count} 张最终绘本图片")

                return final_count > 0
            else:
                print("❌ 绘本图片生成失败")
                print("错误输出:", stderr.decode('utf-8', errors='ignore'))
                return False

        except Exception as e:
            print(f"❌ 绘本图片生成过程出错: {e}")
            return False

    async def run_full_pipeline(self):
        """运行完整的绘本生成流程"""
        print("🚀 开始全自动绘本生成流程")
        print(f"📖 使用绘本文件: {self.huiben_file}")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 预检查
        if not self._check_huiben_file():
            return False

        if not self._check_mask_file():
            return False

        # 执行三个步骤
        success = True

        # 步骤1: 豆包生成图片
        if success:
            success = await self.step1_generate_images_with_doubao()

        # 步骤2: 去除水印
        if success:
            success = self.step2_remove_watermarks()

        # 步骤3: 生成最终绘本
        if success:
            success = await self.step3_generate_final_huiben()

        # 总结
        print("\n" + "="*50)
        if success:
            print("🎉 全自动绘本生成流程完成!")
            print(f"📁 最终图片保存在: {self.final_output_dir}")
            print("✨ 您的绘本已经准备好了!")
        else:
            print("❌ 绘本生成流程失败")
            print("请检查上述错误信息并重试")

        print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*50)

        return success


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="全自动绘本生成脚本")

    parser.add_argument('-f', '--file', type=str, default='huiben.md',
                      help='绘本文件路径 (默认: huiben.md)')

    return parser.parse_args()


async def main():
    """主函数"""
    args = parse_args()

    # 创建绘本生成器实例
    generator = HuibenGenerator(huiben_file=args.file)

    # 运行完整流程
    success = await generator.run_full_pipeline()

    # 退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())
