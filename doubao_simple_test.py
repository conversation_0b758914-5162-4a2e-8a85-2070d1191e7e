#!/usr/bin/env python
# -*- coding: utf-8 -*-

import asyncio
import os
import time
import json
from datetime import datetime

from playwright.async_api import async_playwright, TimeoutError, Error

# 添加pyperclip库用于操作剪贴板
try:
    import pyperclip
    PYPERCLIP_AVAILABLE = True
except ImportError:
    PYPERCLIP_AVAILABLE = False
    print("注意: pyperclip库未安装，将使用备选方法。可以通过pip install pyperclip安装")

# 创建输出目录
OUTPUT_DIR = "test_images"
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 保存登录状态的文件
AUTH_FILE = os.path.abspath("doubao_auth_state.json")
CONTEXT_DIR = os.path.abspath("browser_context")
os.makedirs(CONTEXT_DIR, exist_ok=True)

# 更长的超时时间
LONG_TIMEOUT = 120000  # 120秒
STANDARD_TIMEOUT = 60000  # 60秒
SHORT_TIMEOUT = 30000  # 30秒

# 最大重试次数
MAX_RETRIES = 3

async def wait_with_timeout(page, condition, timeout_ms, description):
    """带超时和重试的等待函数"""
    start_time = time.time()
    end_time = start_time + (timeout_ms / 1000)

    print(f"等待{description}...")

    for retry in range(MAX_RETRIES):
        try:
            result = await condition
            print(f"成功: {description}")
            return result
        except TimeoutError:
            current_time = time.time()
            if current_time >= end_time:
                print(f"超时: {description}，已重试{retry+1}次")
                if retry == MAX_RETRIES - 1:
                    # 最后一次重试，截图记录状态
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    error_path = os.path.join(OUTPUT_DIR, f"timeout_{description.replace(' ', '_')}_{timestamp}.png")
                    await page.screenshot(path=error_path)
                    print(f"已保存超时状态截图: {error_path}")
                    raise

                # 尝试刷新页面
                if retry > 0:
                    print(f"尝试刷新页面...")
                    await page.reload(timeout=STANDARD_TIMEOUT)
            else:
                # 还有时间，暂停后重试
                print(f"暂时未成功: {description}，等待5秒后重试...")
                await asyncio.sleep(5)
        except Error as e:
            print(f"操作出错: {description}, 错误: {e}")
            # 截图记录状态
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            error_path = os.path.join(OUTPUT_DIR, f"error_{description.replace(' ', '_')}_{timestamp}.png")
            await page.screenshot(path=error_path)
            print(f"已保存错误状态截图: {error_path}")
            raise

async def safe_click(page, element, description):
    """安全点击元素，包含重试逻辑"""
    for retry in range(MAX_RETRIES):
        try:
            await element.click(timeout=SHORT_TIMEOUT)
            print(f"成功点击: {description}")
            # 点击后等待页面稳定
            await asyncio.sleep(2)
            return True
        except Error as e:
            print(f"点击失败: {description}, 错误: {e}, 重试{retry+1}/{MAX_RETRIES}")
            if retry == MAX_RETRIES - 1:
                # 最后一次重试，截图记录状态
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                error_path = os.path.join(OUTPUT_DIR, f"click_failed_{description.replace(' ', '_')}_{timestamp}.png")
                await page.screenshot(path=error_path)
                print(f"已保存点击失败截图: {error_path}")
                return False
            await asyncio.sleep(2)  # 等待2秒后重试

async def main():
    print("测试豆包网站图片生成流程...")

    async with async_playwright() as p:
        browser = None
        try:
            # 尝试使用系统安装的Chrome
            print("启动Chrome浏览器...")
            browser = await p.chromium.launch(
                channel='chrome',  # 使用系统安装的Chrome
                headless=False,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=IsolateOrigins,site-per-process'
                ]
            )

            context = None
            new_session = False

            # 检查是否存在保存的登录状态
            auth_file_exists = os.path.isfile(AUTH_FILE)
            if auth_file_exists:
                print(f"找到保存的登录状态文件: {AUTH_FILE}")
                try:
                    # 读取状态文件内容并打印
                    with open(AUTH_FILE, 'r', encoding='utf-8') as f:
                        auth_content = json.load(f)
                        print(f"状态文件包含 {len(auth_content.get('cookies', []))} 个cookies")

                    # 创建新上下文并加载保存的状态
                    context = await browser.new_context(
                        storage_state=AUTH_FILE,
                        viewport={"width": 1280, "height": 800},
                        user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                        ignore_https_errors=True,
                        accept_downloads=True
                    )
                    print("成功加载登录状态")
                except Exception as e:
                    print(f"加载登录状态失败: {e}，将创建新会话")
                    auth_file_exists = False
                    new_session = True

            if not auth_file_exists or new_session:
                # 创建新页面
                print("创建新页面...")
                context = await browser.new_context(
                    viewport={"width": 1280, "height": 800},
                    user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    ignore_https_errors=True,
                    accept_downloads=True
                )

            # 设置默认超时时间
            context.set_default_timeout(LONG_TIMEOUT)

            # 设置下载行为
            await context.set_extra_http_headers({
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            })

            page = await context.new_page()

            try:
                # 访问豆包网站
                print("访问豆包网站...")
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

                # 第一次尝试访问
                for retry in range(MAX_RETRIES):
                    try:
                        response = await page.goto("https://www.doubao.com/chat/", timeout=LONG_TIMEOUT)
                        if response and response.status >= 200 and response.status < 400:
                            print(f"成功访问豆包网站，HTTP状态: {response.status}")
                            break
                        else:
                            status = response.status if response else "未知"
                            print(f"访问豆包网站返回错误状态: {status}，重试{retry+1}/{MAX_RETRIES}")
                            if retry < MAX_RETRIES - 1:
                                await asyncio.sleep(5)  # 等待5秒后重试
                    except Error as e:
                        print(f"访问豆包网站出错: {e}，重试{retry+1}/{MAX_RETRIES}")
                        if retry < MAX_RETRIES - 1:
                            await asyncio.sleep(5)  # 等待5秒后重试
                        else:
                            raise

                # 等待页面加载完成
                print("等待页面完全加载...")
                try:
                    # 尝试多种等待策略
                    await page.wait_for_load_state("domcontentloaded", timeout=SHORT_TIMEOUT)
                    print("DOM内容已加载")

                    await page.wait_for_load_state("load", timeout=STANDARD_TIMEOUT)
                    print("页面资源已加载")

                    # 等待网络活动停止
                    try:
                        await page.wait_for_load_state("networkidle", timeout=STANDARD_TIMEOUT)
                        print("网络活动已停止")
                    except TimeoutError:
                        print("等待网络活动停止超时，继续测试")
                except TimeoutError as e:
                    print(f"等待页面加载超时: {e}，继续测试")

                # 强制等待确保页面渲染
                print("额外等待5秒确保页面渲染...")
                await asyncio.sleep(5)

                # 截图页面
                loaded_path = os.path.join(OUTPUT_DIR, f"doubao_loaded_{timestamp}.png")
                await page.screenshot(path=loaded_path)
                print(f"已保存加载页面截图: {loaded_path}")

                # 检查是否需要登录
                print("检查是否需要登录...")
                login_found = False

                # 更全面的登录检测，使用普通函数而不是async lambda
                async def check_login_button():
                    try:
                        # 首先检查是否已经登录（积极检查）
                        logged_in_indicators = [
                            ".user-avatar",
                            ".avatar",
                            ".user-info",
                            ".user-profile",
                            ".avatar-img",
                            "[aria-label='用户设置']",
                            "[data-testid='avatar']"
                        ]

                        for indicator in logged_in_indicators:
                            if await page.query_selector(indicator):
                                print(f"检测到已登录指标: {indicator}")
                                return False  # 已登录

                        # 检查各种登录按钮（消极检查）
                        login_indicators = [
                            "text=登录",
                            "button:has-text('登录')",
                            "[aria-label='登录']",
                            "a:has-text('登录')",
                            ".login-required",
                            ".need-login"
                        ]

                        for indicator in login_indicators:
                            if await page.query_selector(indicator):
                                print(f"检测到登录指标: {indicator}")
                                return True  # 需要登录

                        # 如果没有明确迹象，假设已登录
                        print("未检测到任何登录或已登录指标，假设已登录")
                        return False
                    except Exception as e:
                        print(f"登录检测出错: {e}")
                        return False

                # 执行登录检查
                login_found = await check_login_button()

                if login_found:
                    print("检测到需要登录，请在浏览器中完成登录...")
                    print("等待30秒，请在此期间完成登录...")

                    # 保存登录前截图
                    login_before_path = os.path.join(OUTPUT_DIR, f"before_login_{timestamp}.png")
                    await page.screenshot(path=login_before_path)
                    print(f"已保存登录前截图: {login_before_path}")

                    # 等待用户手动登录
                    await asyncio.sleep(30)  # 从90秒改为30秒

                    # 登录后截图
                    login_path = os.path.join(OUTPUT_DIR, f"after_login_{timestamp}.png")
                    await page.screenshot(path=login_path)
                    print(f"已保存登录后截图: {login_path}")

                    # 登录成功后保存状态
                    print("正在保存登录状态...")

                    # 先保存浏览器状态
                    await context.storage_state(path=AUTH_FILE)

                    # 打印保存的状态信息
                    with open(AUTH_FILE, 'r', encoding='utf-8') as f:
                        auth_content = json.load(f)
                        print(f"保存了 {len(auth_content.get('cookies', []))} 个cookies")

                    print(f"登录状态已保存到: {AUTH_FILE}")

                    # 确认登录成功
                    print("检查登录是否成功...")
                    # 强制刷新页面
                    await page.reload()
                    await page.wait_for_load_state("networkidle", timeout=SHORT_TIMEOUT)

                    # 再次检查登录状态
                    login_needed = await check_login_button()

                    if login_needed:
                        print("登录可能未成功，请检查登录状态")
                    else:
                        print("登录确认成功")
                else:
                    print("未检测到登录按钮，可能已登录或页面结构发生变化")

                    # 如果使用的是之前保存的状态且没有检测到登录按钮，确认登录状态有效
                    if auth_file_exists:
                        print("已使用保存的登录状态，登录成功!")
                    else:
                        # 如果是新会话但没有检测到登录按钮，仍然保存状态
                        print("新会话无需登录，保存当前状态...")
                        await context.storage_state(path=AUTH_FILE)
                        print(f"状态已保存到: {AUTH_FILE}")

                # 查找图像生成入口
                print("查找图像生成入口...")
                image_selectors = [
                    "div[title='图像生成']",  # 根据用户提供的HTML添加精确选择器
                    ".section-item-title-BjpNe2",  # 使用class选择器
                    "div.title-IK319y[title='图像生成']",  # 组合选择器
                    "div:has-text('图像生成')",  # 文本选择器
                    "text=图像生成",
                    "text=图像",
                    "text=AI绘画",
                    "text=绘画",
                    "text=创作图像",
                    "[aria-label='图像生成']",
                    "button:has-text('绘画')",
                    "button:has-text('创作')",
                    ".sidebar-item:has-text('图像')",
                    "a:has-text('图像')"
                ]

                image_button = None
                for selector in image_selectors:
                    try:
                        # 使用较短超时快速检查多个选择器
                        image_button = await page.wait_for_selector(selector, timeout=5000, state="visible")
                        if image_button:
                            print(f"找到图像生成入口: {selector}")
                            break
                    except:
                        continue

                if not image_button:
                    print("未找到图像生成入口，尝试等待更长时间...")
                    # 再次尝试寻找，使用更长超时
                    for selector in image_selectors:
                        try:
                            image_button = await page.wait_for_selector(selector, timeout=20000, state="visible")
                            if image_button:
                                print(f"在延长等待后找到图像生成入口: {selector}")
                                break
                        except:
                            continue

                if image_button:
                    # 点击图像生成入口前截图
                    before_click_path = os.path.join(OUTPUT_DIR, f"before_click_image_{timestamp}.png")
                    await page.screenshot(path=before_click_path)
                    print(f"已保存点击前截图: {before_click_path}")

                    # 尝试使用JavaScript点击元素
                    try:
                        print("尝试使用JavaScript点击图像生成入口...")
                        # 获取元素的选择器
                        selector = await page.evaluate("""(element) => {
                            // 获取独特的选择器
                            if (element.id) {
                                return '#' + element.id;
                            }
                            if (element.title) {
                                return `div[title="${element.title}"]`;
                            }
                            if (element.className) {
                                return '.' + element.className.split(' ').join('.');
                            }
                            return '';
                        }""", image_button)

                        if selector:
                            # 使用JavaScript直接点击
                            await page.evaluate(f"""(selector) => {{
                                const element = document.querySelector(selector);
                                if (element) {{
                                    element.click();
                                    return true;
                                }}
                                return false;
                            }}""", selector)
                            print(f"使用JavaScript成功点击图像生成入口: {selector}")
                            click_success = True
                        else:
                            # 尝试常规点击
                            click_success = await safe_click(page, image_button, "图像生成入口")
                    except Exception as e:
                        print(f"JavaScript点击失败: {e}")
                        # 尝试常规点击作为备选方案
                        click_success = await safe_click(page, image_button, "图像生成入口")

                    if click_success:
                        # 等待页面变化
                        print("等待图像生成页面加载...")
                        try:
                            await page.wait_for_load_state("networkidle", timeout=SHORT_TIMEOUT)
                        except TimeoutError:
                            print("等待图像生成页面加载超时，继续测试")

                        # 强制等待确保页面渲染
                        print("额外等待5秒确保页面渲染...")
                        await asyncio.sleep(5)

                        # 截图图像生成页面
                        image_page_path = os.path.join(OUTPUT_DIR, f"image_page_{timestamp}.png")
                        await page.screenshot(path=image_page_path)
                        print(f"已保存图像生成页面截图: {image_page_path}")

                        # 查找输入框
                        print("查找输入框...")
                        input_selectors = [
                            "[data-slate-editor='true']",  # Slate编辑器选择器
                            "[data-testid='chat_input_input']",  # 聊天输入框测试ID
                            "[role='textbox']",
                            ".editor-hLysVw",
                            "div[contenteditable='true']",
                            "textarea",
                            "[contenteditable='true']",
                            ".prompt-box textarea",
                            ".input-area textarea",
                            "textarea[placeholder]"
                        ]

                        input_box = None
                        for selector in input_selectors:
                            try:
                                input_box = await page.query_selector(selector)
                                if input_box:
                                    print(f"找到输入框: {selector}")
                                    break
                            except:
                                continue

                        if input_box:
                            print("找到输入框，读取huiben.md文件作为提示词...")

                            # 读取huiben.md文件内容作为提示词
                            try:
                                with open("huiben.md", "r", encoding="utf-8") as f:
                                    huiben_content = f.read()
                                print("成功读取huiben.md文件内容")
                                test_prompt = huiben_content
                            except FileNotFoundError:
                                print("警告：未找到huiben.md文件，使用默认提示词")
                                test_prompt = "童书插画，水彩画风格：阳光下的彩虹森林全景，树冠漏下金色光斑，高质量儿童读物插图，柔和色调，温暖光感"
                            except Exception as e:
                                print(f"读取huiben.md文件失败: {e}，使用默认提示词")
                                test_prompt = "童书插画，水彩画风格：阳光下的彩虹森林全景，树冠漏下金色光斑，高质量儿童读物插图，柔和色调，温暖光感"

                            # 使用复制粘贴方法输入提示词
                            try:
                                print("使用复制粘贴方法输入...")

                                # 方法1: 使用pyperclip库复制到系统剪贴板
                                original_clipboard = None
                                if PYPERCLIP_AVAILABLE:
                                    try:
                                        # 保存原始剪贴板内容
                                        original_clipboard = pyperclip.paste()
                                        # 设置新的剪贴板内容
                                        pyperclip.copy(test_prompt)
                                        print("已复制提示词到系统剪贴板")
                                    except Exception as e:
                                        print(f"使用pyperclip复制失败: {e}")

                                # 清空编辑器内容
                                await page.evaluate("""() => {
                                    const editors = document.querySelectorAll('[data-slate-editor="true"]');
                                    for (let editor of editors) {
                                        editor.innerHTML = '';
                                        console.log("已清空编辑器内容");
                                    }
                                }""")

                                # 点击以聚焦输入框
                                await input_box.click()
                                await asyncio.sleep(1)

                                # 方法2: 使用Playwright的键盘快捷键模拟粘贴操作
                                print("尝试使用键盘快捷键粘贴...")

                                # 检测操作系统类型，使用相应的快捷键
                                import platform
                                if platform.system() == 'Darwin':  # macOS
                                    # 使用Command+V
                                    await page.keyboard.press("Meta+V")
                                else:
                                    # 使用Ctrl+V
                                    await page.keyboard.press("Control+V")

                                await asyncio.sleep(2)

                                # 检查粘贴是否成功
                                text_content = await page.evaluate("""() => {
                                    const spans = document.querySelectorAll('span[data-slate-string="true"]');
                                    if (spans && spans.length > 0) {
                                        return spans[0].textContent || '';
                                    }
                                    return '';
                                }""")

                                print(f"粘贴后文本内容: {text_content[:20]}...")

                                # 如果粘贴不成功，尝试方法3: 使用JavaScript的execCommand
                                if not text_content:
                                    print("键盘快捷键粘贴失败，尝试使用execCommand方法...")

                                    # 在页面中创建一个隐藏的textarea元素
                                    paste_result = await page.evaluate("""(text) => {
                                        // 创建一个临时textarea存放文本
                                        const textarea = document.createElement('textarea');
                                        textarea.value = text;
                                        document.body.appendChild(textarea);
                                        textarea.select();

                                        // 尝试使用document.execCommand执行粘贴
                                        let success = false;
                                        try {
                                            // 使用execCommand复制文本到剪贴板
                                            if (document.execCommand('copy')) {
                                                console.log("成功复制到剪贴板");

                                                // 聚焦到Slate编辑器
                                                const editor = document.querySelector('[data-slate-editor="true"]');
                                                if (editor) {
                                                    editor.focus();
                                                    // 尝试粘贴
                                                    if (document.execCommand('paste')) {
                                                        console.log("execCommand粘贴成功");
                                                        success = true;
                                                    }
                                                }
                                            }
                                        } catch (e) {
                                            console.error("execCommand粘贴失败:", e);
                                        }

                                        // 移除临时元素
                                        document.body.removeChild(textarea);

                                        // 如果粘贴失败，直接设置内容
                                        if (!success) {
                                            try {
                                                const spans = document.querySelectorAll('span[data-slate-string="true"]');
                                                if (spans && spans.length > 0) {
                                                    spans[0].textContent = text;
                                                    console.log("直接设置文本内容");

                                                    // 触发必要的事件
                                                    const editor = document.querySelector('[data-slate-editor="true"]');
                                                    if (editor) {
                                                        ['input', 'change'].forEach(event =>
                                                            editor.dispatchEvent(new Event(event, {bubbles: true}))
                                                        );
                                                    }
                                                    success = true;
                                                }
                                            } catch (e) {
                                                console.error("直接设置文本失败:", e);
                                            }
                                        }

                                        return { success, method: success ? (success === true ? "execCommand或直接设置" : "键盘快捷键") : "全部失败" };
                                    }""", test_prompt)

                                    print(f"execCommand粘贴结果: {paste_result}")

                                # 截图记录输入状态
                                input_path = os.path.join(OUTPUT_DIR, f"input_complete_{timestamp}.png")
                                await page.screenshot(path=input_path)
                                print(f"已保存输入完成截图: {input_path}")

                                # 尝试激活发送按钮
                                print("尝试激活发送按钮...")
                                button_result = await page.evaluate("""() => {
                                    const button = document.querySelector('#flow-end-msg-send');
                                    if (!button) return { success: false, error: "未找到按钮" };

                                    try {
                                        // 检查按钮状态
                                        const beforeState = {
                                            disabled: button.disabled,
                                            ariaDisabled: button.getAttribute('aria-disabled'),
                                            classList: button.className
                                        };

                                        // 移除禁用状态
                                        button.disabled = false;
                                        button.removeAttribute('disabled');
                                        button.removeAttribute('aria-disabled');

                                        // 修改CSS类
                                        button.className = button.className
                                            .replace('semi-button-disabled', 'semi-button')
                                            .replace('semi-button-primary-disabled', 'semi-button-primary');

                                        // 尝试点击
                                        button.click();

                                        // 触发点击事件
                                        const clickEvent = new MouseEvent('click', {
                                            bubbles: true,
                                            cancelable: true,
                                            view: window
                                        });
                                        button.dispatchEvent(clickEvent);

                                        // 检查修改后状态
                                        const afterState = {
                                            disabled: button.disabled,
                                            ariaDisabled: button.getAttribute('aria-disabled'),
                                            classList: button.className
                                        };

                                        return {
                                            success: true,
                                            beforeState,
                                            afterState
                                        };
                                    } catch (e) {
                                        return { success: false, error: e.toString() };
                                    }
                                }""")

                                print(f"按钮激活结果: {button_result}")

                                # 额外等待以检查是否有延迟效果
                                await asyncio.sleep(5)

                                # 再次截图
                                after_path = os.path.join(OUTPUT_DIR, f"after_button_click_{timestamp}.png")
                                await page.screenshot(path=after_path)
                                print(f"已保存按钮点击后截图: {after_path}")

                                # 等待图像生成完成
                                print("等待图像生成过程...")

                                # 增加等待时间，确保有足够时间完成生成
                                max_wait_time = 180  # 最长等待3分钟
                                check_interval = 5   # 每5秒检查一次

                                # 监听下载事件
                                page.on("download", lambda download: print(f"检测到下载事件: {download.suggested_filename}"))

                                # 等待图像生成完成
                                image_generated = False
                                generated_image_info = None

                                for i in range(max_wait_time // check_interval):
                                    print(f"等待图片生成中... ({i+1}/{max_wait_time // check_interval})")

                                    # 截图当前状态
                                    if i % 3 == 0:  # 每15秒保存一次截图
                                        waiting_path = os.path.join(OUTPUT_DIR, f"waiting_for_image_{i}_{timestamp}.png")
                                        await page.screenshot(path=waiting_path)
                                        print(f"已保存等待状态截图: {waiting_path}")

                                    # 检测图像生成完成：判断break按钮是否可见
                                    if not image_generated:
                                        generation_check_result = await page.evaluate("""() => {
                                            // 查找break按钮
                                            const breakButton = document.querySelector('[data-testid="chat_input_local_break_button"]');
                                            if (!breakButton) {
                                                console.log("未找到break按钮");
                                                return { found: false, status: "no_break_button" };
                                            }

                                            // 检查按钮的class是否包含!hidden
                                            const classList = breakButton.className || '';
                                            const hasNotHidden = classList.includes('!hidden');

                                            console.log("Break按钮class:", classList);
                                            console.log("包含!hidden:", hasNotHidden);

                                            if (hasNotHidden) {
                                                console.log("检测到图像生成完成（break按钮包含!hidden）");
                                                return {
                                                    found: true,
                                                    status: "generation_complete",
                                                    buttonClass: classList
                                                };
                                            } else {
                                                console.log("图像仍在生成中（break按钮不包含!hidden）");
                                                return {
                                                    found: false,
                                                    status: "generating",
                                                    buttonClass: classList
                                                };
                                            }
                                        }""")

                                        print(f"图像生成检查结果: {generation_check_result}")

                                        # 如果检测到生成完成
                                        if generation_check_result and generation_check_result.get("found", False):
                                            print(f"✅ 检测到图像生成完成: {generation_check_result}")
                                            image_generated = True
                                            generated_image_info = {"status": "complete"}

                                            # 保存生成结果截图
                                            result_path = os.path.join(OUTPUT_DIR, f"generation_result_{timestamp}.png")
                                            await page.screenshot(path=result_path)
                                            print(f"已保存生成结果截图: {result_path}")

                                            # 继续到下载阶段
                                            continue

                                    # 第二阶段：图像生成完成后，使用新的下载逻辑
                                    if image_generated and generated_image_info:
                                        print("🔍 开始使用新的下载逻辑下载所有图片...")

                                        # 查找所有下载按钮（使用doubao_simple_download.py的逻辑）
                                        print("🔍 查找所有下载按钮...")
                                        all_buttons = await page.query_selector_all('[data-testid="edit_image_hover_tag_download_btn"]')
                                        print(f"📊 找到 {len(all_buttons)} 个下载按钮")

                                        if len(all_buttons) == 0:
                                            print("❌ 没有找到任何下载按钮，检查页面结构...")

                                            # 分析页面结构
                                            page_analysis = await page.evaluate("""() => {
                                                const containers = document.querySelectorAll('[class*="image-box-grid-item"]');
                                                const analysis = {
                                                    containerCount: containers.length,
                                                    containers: []
                                                };

                                                containers.forEach((container, index) => {
                                                    const testIds = Array.from(container.querySelectorAll('[data-testid]')).map(el => el.getAttribute('data-testid'));
                                                    analysis.containers.push({
                                                        index: index,
                                                        className: container.className,
                                                        testIds: testIds,
                                                        hasDownloadBtn: !!container.querySelector('[data-testid="edit_image_hover_tag_download_btn"]')
                                                    });
                                                });

                                                return analysis;
                                            }""")

                                            print(f"🔍 页面分析结果: {page_analysis}")
                                            # 如果没有找到下载按钮，继续等待
                                            continue

                                        # 强制显示所有下载按钮
                                        print("🔧 强制显示所有下载按钮...")
                                        await page.evaluate("""() => {
                                            const buttons = document.querySelectorAll('[data-testid="edit_image_hover_tag_download_btn"]');
                                            buttons.forEach(btn => {
                                                // 强制显示按钮
                                                btn.style.display = 'block';
                                                btn.style.visibility = 'visible';
                                                btn.style.opacity = '1';

                                                // 移除可能的隐藏类
                                                btn.classList.remove('hidden');

                                                // 确保父元素也可见
                                                let parent = btn.parentElement;
                                                while (parent) {
                                                    parent.style.display = 'block';
                                                    parent.style.visibility = 'visible';
                                                    parent.style.opacity = '1';
                                                    parent = parent.parentElement;
                                                }
                                            });

                                            console.log(`强制显示了 ${buttons.length} 个下载按钮`);
                                        }""")

                                        # 等待一下让样式生效
                                        await asyncio.sleep(2)

                                        # 重新获取按钮列表
                                        all_buttons = await page.query_selector_all('[data-testid="edit_image_hover_tag_download_btn"]')
                                        print(f"🔄 重新获取到 {len(all_buttons)} 个下载按钮")

                                        # 逐个点击下载按钮并保存文件
                                        download_count = 0
                                        download_path = os.path.abspath(OUTPUT_DIR)

                                        for i, button in enumerate(all_buttons):
                                            print(f"\n🖼️ 处理第 {i+1} 个下载按钮...")

                                            try:
                                                # 检查按钮状态
                                                is_visible = await button.is_visible()
                                                is_enabled = await button.is_enabled()
                                                print(f"   按钮状态: 可见={is_visible}, 可用={is_enabled}")

                                                # 设置下载监听并点击
                                                try:
                                                    # 监听下载事件
                                                    async with page.expect_download(timeout=15000) as download_info:
                                                        await button.click(force=True)
                                                        print("✅ 已点击下载按钮，等待下载...")

                                                    # 获取下载对象
                                                    download = await download_info.value
                                                    print(f"📥 检测到下载: {download.suggested_filename}")

                                                    # 保存到指定文件夹
                                                    filename = f"image_{i+1}_{timestamp}_{download.suggested_filename}"
                                                    save_path = os.path.join(download_path, filename)

                                                    await download.save_as(save_path)
                                                    print(f"💾 已保存到: {save_path}")
                                                    download_count += 1

                                                except Exception as download_error:
                                                    print(f"❌ 下载失败: {download_error}")
                                                    # 如果下载监听失败，仍然点击按钮（可能会下载到默认文件夹）
                                                    try:
                                                        await button.click(force=True)
                                                        print("✅ 已点击下载按钮（可能下载到默认文件夹）")
                                                    except Exception as click_error:
                                                        print(f"❌ 点击失败: {click_error}")

                                            except Exception as e:
                                                print(f"❌ 处理第 {i+1} 个按钮失败: {e}")

                                            # 等待一下，避免操作过快
                                            await asyncio.sleep(2)

                                        print(f"\n🎉 完成！成功点击了 {download_count} 个下载按钮")
                                        print("📁 请检查浏览器的下载文件夹查看下载的图片")

                                        # 找到图像和处理下载后跳出循环
                                        break

                                    # 等待一段时间后再次检查
                                    await asyncio.sleep(check_interval)

                                # 最终状态截图
                                result_path = os.path.join(OUTPUT_DIR, f"final_state_{timestamp}.png")
                                await page.screenshot(path=result_path)
                                print(f"已保存最终状态截图: {result_path}")

                                # 恢复原始剪贴板内容
                                if PYPERCLIP_AVAILABLE and original_clipboard is not None:
                                    try:
                                        pyperclip.copy(original_clipboard)
                                        print("已恢复原始剪贴板内容")
                                    except:
                                        pass

                            except Error as e:
                                print(f"复制粘贴方法失败: {e}")
                                # 截图错误状态
                                error_path = os.path.join(OUTPUT_DIR, f"input_error_{timestamp}.png")
                                await page.screenshot(path=error_path)
                                print(f"已保存错误状态截图: {error_path}")
                        else:
                            print("未找到输入框")
                    else:
                        print("点击图像生成入口失败")
                else:
                    print("未找到图像生成入口")

                # 最后等待一段时间让用户查看
                print("测试完成，等待30秒让用户查看...")
                await asyncio.sleep(30)

            except Exception as e:
                print(f"测试过程中出错: {e}")
                try:
                    # 截图错误状态
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    error_path = os.path.join(OUTPUT_DIR, f"error_{timestamp}.png")
                    await page.screenshot(path=error_path)
                    print(f"已保存错误状态截图: {error_path}")
                except:
                    pass

            # 关闭浏览器
            print("关闭浏览器...")
            if browser:
                await browser.close()

        except Exception as e:
            print(f"浏览器启动过程中出错: {e}")
            if browser:
                await browser.close()

if __name__ == "__main__":
    asyncio.run(main())