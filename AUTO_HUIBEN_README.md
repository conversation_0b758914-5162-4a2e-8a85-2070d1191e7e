# 全自动绘本生成脚本使用说明

## 概述

这个全自动绘本生成脚本整合了三个核心功能，实现一键生成完整绘本的流程：

1. **豆包AI图片生成** - 使用豆包网站生成原始图片
2. **水印去除** - 自动去除生成图片中的水印
3. **绘本模板生成** - 将图片制作成小红书风格的绘本图片

## 功能特点

- 🤖 **全自动化**: 一键完成从文本到绘本的全部流程
- 📖 **智能解析**: 自动从huiben.md文件提取分镜内容
- 🎨 **专业模板**: 生成符合小红书审美的4:3竖版绘本图片
- 🔧 **智能重命名**: 自动整理和重命名下载的图片
- 📁 **目录管理**: 自动创建和管理各阶段的输出目录
- ⚡ **错误处理**: 完善的错误检测和恢复机制

## 文件结构

```
项目目录/
├── auto_generate_huiben.py    # 主脚本
├── run_auto_huiben.sh         # Linux/macOS启动脚本
├── run_auto_huiben.bat        # Windows启动脚本
├── huiben.md                  # 绘本内容文件
├── doubao_simple_test.py      # 豆包图片生成脚本
├── remove_watermark_precise.py # 水印去除脚本
├── generate_images.py         # 绘本模板生成脚本
├── template.html              # 绘本模板文件
├── mask/
│   └── mask.jpg              # 水印蒙版文件
├── test_images/              # 豆包生成的原始图片
├── output/                   # 去水印后的图片
└── output_images/            # 最终绘本图片
```

## 安装要求

### 系统要求
- Python 3.7 或更高版本
- Chrome 浏览器（用于Playwright）
- 稳定的网络连接

### Python依赖
```bash
pip install playwright jinja2 asyncio subprocess pathlib
```

### 安装Playwright浏览器
```bash
playwright install chrome
```

### 安装iopaint（用于去水印）
```bash
pip install iopaint
```

## 使用方法

### 0. 环境检查（推荐）

在开始之前，建议先运行环境检查脚本：
```bash
python test_auto_huiben.py
```
这会检查所有必要的文件、依赖和配置是否正确。

### 1. 准备工作

1. **准备绘本内容文件**
   - 确保 `huiben.md` 文件存在并包含正确格式的内容
   - 文件应包含9个分镜的描述

2. **准备水印蒙版**
   - 确保 `mask/mask.jpg` 文件存在
   - 蒙版应该标记出需要去除的水印区域

### 2. 运行脚本

#### 演示模式（推荐新手）
如果您想先测试整个流程，可以使用演示模式：
```bash
python demo_auto_huiben.py
```
演示模式会使用现有的图片文件，跳过豆包网站生成步骤，直接演示水印去除和绘本生成流程。

#### 完整模式

#### Linux/macOS用户
```bash
# 使用默认的huiben.md文件
./run_auto_huiben.sh

# 或指定特定的绘本文件
./run_auto_huiben.sh my_huiben.md
```

#### Windows用户
```cmd
# 使用默认的huiben.md文件
run_auto_huiben.bat

# 或指定特定的绘本文件
run_auto_huiben.bat my_huiben.md
```

#### 直接运行Python脚本
```bash
python auto_generate_huiben.py --file huiben.md
```

### 3. 执行流程

脚本会自动执行以下步骤：

1. **预检查**
   - 检查huiben.md文件是否存在
   - 检查水印蒙版文件是否存在
   - 创建必要的目录

2. **步骤1: 豆包图片生成**
   - 启动浏览器访问豆包网站
   - 如需登录，会提示用户在浏览器中完成登录
   - 自动输入绘本内容并生成图片
   - 下载生成的图片到 `test_images` 目录
   - 自动重命名为标准格式 (1.png, 2.png, ...)

3. **步骤2: 去除水印**
   - 使用iopaint工具去除图片水印
   - 输入: `test_images` 目录
   - 输出: `output` 目录

4. **步骤3: 生成绘本模板**
   - 使用去水印后的图片生成最终绘本
   - 输入: `output` 目录
   - 输出: `output_images` 目录

## 输出结果

成功执行后，您将在以下目录找到结果：

- `test_images/` - 豆包生成的原始图片
- `output/` - 去水印后的图片
- `output_images/` - 最终的绘本图片 (xiaohongshu_frame_1.png 到 xiaohongshu_frame_9.png)

## 注意事项

### 豆包网站登录
- 首次运行时，如果豆包网站需要登录，脚本会暂停30秒
- 请在此期间在浏览器中完成登录操作
- 登录状态会被保存，后续运行无需重复登录

### 网络和时间
- 图片生成过程可能需要几分钟时间
- 请确保网络连接稳定
- 脚本会自动等待图片生成完成

### 文件命名
- 下载的图片会自动重命名为 1.png, 2.png, ... 9.png
- 最终绘本图片命名为 xiaohongshu_frame_1.png 到 xiaohongshu_frame_9.png

## 故障排除

### 常见问题

1. **"未找到Python"错误**
   - 确保已安装Python 3.7+
   - 确保Python在系统PATH中

2. **"未安装playwright"错误**
   - 运行: `pip install playwright`
   - 运行: `playwright install chrome`

3. **"找不到huiben.md文件"错误**
   - 确保文件存在于当前目录
   - 或使用 `--file` 参数指定正确路径

4. **"找不到水印蒙版文件"错误**
   - 确保 `mask/mask.jpg` 文件存在
   - 检查文件路径和权限

5. **豆包网站访问失败**
   - 检查网络连接
   - 确保能正常访问 https://www.doubao.com
   - 可能需要手动登录

6. **水印去除失败**
   - 确保已安装iopaint: `pip install iopaint`
   - 检查蒙版文件是否正确

### 调试模式

如果遇到问题，可以单独运行各个步骤进行调试：

```bash
# 单独测试豆包图片生成
python doubao_simple_test.py

# 单独测试水印去除
python remove_watermark_precise.py -i test_images -o output -m mask/mask.jpg --debug

# 单独测试绘本生成
python generate_images.py
```

## 自定义配置

### 修改输出目录
编辑 `auto_generate_huiben.py` 中的目录配置：
```python
self.doubao_output_dir = "test_images"      # 豆包输出目录
self.watermark_output_dir = "output"        # 水印去除输出目录
self.final_output_dir = "output_images"     # 最终输出目录
```

### 修改水印去除参数
编辑水印去除命令参数：
```python
cmd = [
    sys.executable, "remove_watermark_precise.py",
    "-i", self.watermark_input_dir,
    "-o", self.watermark_output_dir,
    "-m", self.mask_path,
    "--model", "lama",  # 可选: lama, ldm, zits, mat, fcf
    "--device", "cpu",  # 可选: cpu, cuda
    "--debug"
]
```

## 技术支持

如果遇到问题，请：
1. 检查所有依赖是否正确安装
2. 查看错误日志和截图文件
3. 确认网络连接和文件权限
4. 尝试单独运行各个组件进行调试

---

**祝您使用愉快！** 🎉
