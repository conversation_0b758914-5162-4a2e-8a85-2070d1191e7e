#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试重命名功能的脚本
创建模拟的文件结构来测试重命名逻辑
"""

import os
import shutil
from datetime import datetime
from pathlib import Path

# 导入主脚本中的重命名函数
import sys
sys.path.append('.')
from auto_generate_huiben import HuibenGenerator

def create_test_files():
    """创建测试文件结构"""
    test_dir = "test_rename_dir"
    
    # 清理并创建测试目录
    if os.path.exists(test_dir):
        shutil.rmtree(test_dir)
    os.makedirs(test_dir)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 创建模拟的豆包下载文件（真正的图片文件）
    real_image_files = [
        f"image_1_{timestamp}_generated_image_001.png",
        f"image_2_{timestamp}_ai_artwork_002.jpg", 
        f"image_3_{timestamp}_created_picture_003.png",
        f"image_4_{timestamp}_doubao_image_004.png",
        f"image_5_{timestamp}_generated_art_005.jpg",
        f"image_6_{timestamp}_ai_generated_006.png",
        f"image_7_{timestamp}_created_image_007.png",
        f"image_8_{timestamp}_doubao_art_008.jpg",
        f"image_9_{timestamp}_generated_pic_009.png"
    ]
    
    # 创建模拟的截图文件（应该被排除）
    screenshot_files = [
        f"doubao_loaded_{timestamp}.png",
        f"before_login_{timestamp}.png", 
        f"after_login_{timestamp}.png",
        f"before_click_image_{timestamp}.png",
        f"image_page_{timestamp}.png",
        f"input_complete_{timestamp}.png",
        f"waiting_for_image_{timestamp}.png",
        f"generation_result_{timestamp}.png",
        f"final_state_{timestamp}.png",
        f"error_{timestamp}.png",
        f"click_failed_generate_button_{timestamp}.png",
        f"timeout_waiting_for_generation_{timestamp}.png",
        f"page_screenshot_{timestamp}.png",
        f"debug_info_{timestamp}.png"
    ]
    
    # 创建一些其他文件（应该被排除）
    other_files = [
        "readme.txt",
        "config.json",
        f"random_image_{timestamp}.png",  # 不符合命名规则
        f"some_photo_{timestamp}.jpg"     # 不符合命名规则
    ]
    
    print(f"创建测试目录: {test_dir}")
    print("\n创建真正的图片文件（应该被重命名）:")
    for file in real_image_files:
        file_path = os.path.join(test_dir, file)
        with open(file_path, 'w') as f:
            f.write("fake image content")
        print(f"  ✓ {file}")
    
    print("\n创建截图文件（应该被排除）:")
    for file in screenshot_files:
        file_path = os.path.join(test_dir, file)
        with open(file_path, 'w') as f:
            f.write("fake screenshot content")
        print(f"  ✓ {file}")
    
    print("\n创建其他文件（应该被排除）:")
    for file in other_files:
        file_path = os.path.join(test_dir, file)
        with open(file_path, 'w') as f:
            f.write("fake other content")
        print(f"  ✓ {file}")
    
    return test_dir

def test_rename_function():
    """测试重命名功能"""
    print("🧪 测试重命名功能")
    print("=" * 50)
    
    # 创建测试文件
    test_dir = create_test_files()
    
    print(f"\n📁 测试目录内容 (重命名前):")
    files_before = sorted(os.listdir(test_dir))
    for i, file in enumerate(files_before, 1):
        print(f"  {i:2d}. {file}")
    
    # 创建生成器实例并测试重命名
    generator = HuibenGenerator()
    
    print(f"\n🔄 开始测试重命名...")
    success = generator._rename_downloaded_images(test_dir)
    
    print(f"\n📁 测试目录内容 (重命名后):")
    files_after = sorted(os.listdir(test_dir))
    for i, file in enumerate(files_after, 1):
        print(f"  {i:2d}. {file}")
    
    # 检查结果
    print(f"\n📊 重命名结果分析:")
    print(f"重命名操作成功: {'✅' if success else '❌'}")
    
    # 检查是否有正确的1.png到9.png文件
    expected_files = [f"{i}.png" for i in range(1, 10)] + [f"{i}.jpg" for i in range(1, 10)]
    found_numbered_files = []
    
    for file in files_after:
        if file.split('.')[0].isdigit() and len(file.split('.')[0]) <= 2:
            found_numbered_files.append(file)
    
    print(f"找到的数字命名文件: {found_numbered_files}")
    
    # 检查截图文件是否仍然存在（应该存在，因为没有被重命名）
    screenshot_count = 0
    for file in files_after:
        if any(keyword in file.lower() for keyword in ['doubao_loaded', 'before_login', 'error', 'screenshot']):
            screenshot_count += 1
    
    print(f"保留的截图文件数量: {screenshot_count}")
    
    # 总结
    print(f"\n📋 测试总结:")
    if success and len(found_numbered_files) >= 9:
        print("✅ 重命名功能工作正常")
        print(f"✅ 成功生成了 {len(found_numbered_files)} 个数字命名文件")
        print(f"✅ 保留了 {screenshot_count} 个截图文件（未被误删）")
    else:
        print("❌ 重命名功能存在问题")
        if not success:
            print("  - 重命名操作失败")
        if len(found_numbered_files) < 9:
            print(f"  - 数字命名文件数量不足: {len(found_numbered_files)}/9")
    
    # 清理测试目录
    print(f"\n🧹 清理测试目录: {test_dir}")
    shutil.rmtree(test_dir)
    
    return success and len(found_numbered_files) >= 9

if __name__ == "__main__":
    success = test_rename_function()
    print(f"\n{'🎉 测试通过!' if success else '❌ 测试失败!'}")
    sys.exit(0 if success else 1)
