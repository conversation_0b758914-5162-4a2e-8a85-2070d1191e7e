@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 全自动绘本生成启动脚本 (Windows版)
REM 使用方法: run_auto_huiben.bat [huiben.md文件路径]

echo 🚀 启动全自动绘本生成流程...

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

REM 检查虚拟环境
if exist "venv\Scripts\activate.bat" (
    echo ✓ 发现虚拟环境，正在激活...
    call venv\Scripts\activate.bat
) else (
    echo ⚠️  警告: 未发现虚拟环境，使用系统Python
)

REM 检查依赖
echo 📦 检查依赖...
python -c "import playwright" >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未安装playwright，请运行: pip install playwright
    pause
    exit /b 1
)

python -c "import jinja2" >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未安装jinja2，请运行: pip install jinja2
    pause
    exit /b 1
)

REM 检查huiben.md文件
set "HUIBEN_FILE=%~1"
if "%HUIBEN_FILE%"=="" set "HUIBEN_FILE=huiben.md"

if not exist "%HUIBEN_FILE%" (
    echo ❌ 错误: 找不到绘本文件 '%HUIBEN_FILE%'
    echo 请确保文件存在，或指定正确的文件路径
    echo 使用方法: %0 [huiben.md文件路径]
    pause
    exit /b 1
)

echo ✓ 使用绘本文件: %HUIBEN_FILE%

REM 检查必要文件
if not exist "auto_generate_huiben.py" (
    echo ❌ 错误: 找不到主脚本 auto_generate_huiben.py
    pause
    exit /b 1
)

if not exist "doubao_simple_test.py" (
    echo ❌ 错误: 找不到豆包生成脚本 doubao_simple_test.py
    pause
    exit /b 1
)

if not exist "remove_watermark_precise.py" (
    echo ❌ 错误: 找不到水印去除脚本 remove_watermark_precise.py
    pause
    exit /b 1
)

if not exist "generate_images.py" (
    echo ❌ 错误: 找不到绘本生成脚本 generate_images.py
    pause
    exit /b 1
)

REM 检查水印蒙版文件
if not exist "mask\mask.jpg" (
    echo ❌ 错误: 找不到水印蒙版文件 mask\mask.jpg
    echo 请确保mask目录下有mask.jpg文件
    pause
    exit /b 1
)

echo ✅ 所有检查通过，开始执行...
echo.

REM 运行主脚本
python auto_generate_huiben.py --file "%HUIBEN_FILE%"

REM 检查执行结果
if errorlevel 1 (
    echo.
    echo ❌ 绘本生成过程中出现错误
    echo 请查看上述错误信息并重试
    pause
    exit /b 1
) else (
    echo.
    echo 🎉 全自动绘本生成完成！
    echo 📁 请查看 output_images 目录中的最终结果
    pause
)
