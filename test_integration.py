#!/usr/bin/env python3
"""
测试集成后的doubao_simple_test.py脚本
验证huiben.md文件读取和新下载逻辑是否正确集成
"""

import os
import sys

def test_file_exists():
    """测试必要文件是否存在"""
    print("🔍 检查必要文件...")
    
    files_to_check = [
        "doubao_simple_test.py",
        "huiben.md",
        "doubao_simple_download.py"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 不存在")
            return False
    
    return True

def test_huiben_content():
    """测试huiben.md文件内容读取"""
    print("\n📖 测试huiben.md文件内容读取...")
    
    try:
        with open("huiben.md", "r", encoding="utf-8") as f:
            content = f.read()
        
        print(f"✅ 成功读取huiben.md文件，内容长度: {len(content)} 字符")
        print(f"📝 内容预览: {content[:100]}...")
        
        # 检查是否包含关键内容
        if "彩虹森林" in content and "分镜头" in content:
            print("✅ 文件包含预期的绘本内容")
            return True
        else:
            print("⚠️ 文件内容可能不完整")
            return False
            
    except Exception as e:
        print(f"❌ 读取huiben.md文件失败: {e}")
        return False

def test_script_syntax():
    """测试脚本语法是否正确"""
    print("\n🔧 测试脚本语法...")
    
    try:
        # 尝试编译脚本
        with open("doubao_simple_test.py", "r", encoding="utf-8") as f:
            script_content = f.read()
        
        compile(script_content, "doubao_simple_test.py", "exec")
        print("✅ doubao_simple_test.py 语法检查通过")
        return True
        
    except SyntaxError as e:
        print(f"❌ doubao_simple_test.py 语法错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 检查doubao_simple_test.py 时出错: {e}")
        return False

def test_integration_points():
    """测试集成点是否正确"""
    print("\n🔗 测试集成点...")
    
    try:
        with open("doubao_simple_test.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查关键集成点
        integration_checks = [
            ("huiben.md文件读取", 'with open("huiben.md"'),
            ("新下载逻辑", 'data-testid="edit_image_hover_tag_download_btn"'),
            ("下载按钮强制显示", "强制显示所有下载按钮"),
            ("下载监听", "expect_download"),
            ("下载保存", "save_as")
        ]
        
        all_passed = True
        for check_name, check_pattern in integration_checks:
            if check_pattern in content:
                print(f"✅ {check_name}: 已集成")
            else:
                print(f"❌ {check_name}: 未找到")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查集成点时出错: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试doubao_simple_test.py集成...")
    
    tests = [
        ("文件存在性检查", test_file_exists),
        ("huiben.md内容读取", test_huiben_content),
        ("脚本语法检查", test_script_syntax),
        ("集成点检查", test_integration_points)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n{'='*50}")
    print(f"测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！集成成功！")
        print("\n📋 集成总结:")
        print("1. ✅ 已将doubao_simple_download.py的下载逻辑集成到doubao_simple_test.py")
        print("2. ✅ 已移除旧的下载方式（悬停查找下载按钮）")
        print("3. ✅ 已修改提示词为读取huiben.md文件内容")
        print("4. ✅ 脚本语法正确，可以运行")
        print("\n🚀 现在可以运行 python doubao_simple_test.py 来测试完整功能")
        return True
    else:
        print("❌ 部分测试失败，请检查集成")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
