#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import asyncio
from playwright.async_api import async_playwright
import jinja2

# 定义输出目录
OUTPUT_DIR = "output_images"
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 提取分镜文本内容
def extract_frame_texts(md_file="huiben.md"):
    """从绘本MD文件中提取每个分镜对应的文字内容"""
    print(f"正在从 {md_file} 文件中提取分镜文字内容...")

    frame_texts = []

    try:
        # 读取MD文件
        with open(md_file, 'r', encoding='utf-8') as f:
            content = f.read()

        print("成功读取MD文件，开始提取文字内容...")

        # 按行分割内容
        lines = content.split('\n')

        # 查找所有以"文字："开头的行
        for line in lines:
            line = line.strip()
            if line.startswith('文字：'):
                # 提取"文字："后面的内容
                text_content = line[3:].strip()  # 去掉"文字："前缀

                # 处理多个引号内容的情况
                # 先提取所有引号内的内容
                import re

                # 使用正则表达式提取所有引号内的内容
                # 匹配中文引号和英文引号
                quotes_pattern = r'["""]([^"""]*?)["""]'
                matches = re.findall(quotes_pattern, text_content)

                if matches:
                    # 如果找到引号内容，将它们用逗号连接
                    text_content = '，'.join(matches)
                else:
                    # 如果没有找到引号，直接使用原内容（去掉可能的外层引号）
                    if text_content.startswith('"') and text_content.endswith('"'):
                        text_content = text_content[1:-1]
                    elif text_content.startswith('"') and text_content.endswith('"'):
                        text_content = text_content[1:-1]

                frame_texts.append(text_content)
                print(f"提取到文字内容: {text_content}")

        print(f"成功提取到 {len(frame_texts)} 个分镜的文字内容")


    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        print("使用默认的文字内容...")
        frame_texts = [f"分镜 {i} 的故事内容" for i in range(1, 10)]

    # 输出最终结果
    print("\n最终确定的文字内容:")
    for i, text in enumerate(frame_texts, 1):
        print(f"分镜 {i}: {text}")

    return frame_texts

# 使用Playwright渲染HTML为图片
async def generate_image(frame_number, image_path, text_content):
    """使用Playwright渲染HTML模板为图片"""
    template_loader = jinja2.FileSystemLoader(".")
    template_env = jinja2.Environment(loader=template_loader)
    template = template_env.get_template("template.html")

    # 渲染模板
    html_content = template.render(
        image_path=image_path,
        text_content=text_content,
        page_number=frame_number
    )

    # 保存临时HTML文件
    temp_html = f"temp_{frame_number}.html"
    with open(temp_html, "w", encoding="utf-8") as f:
        f.write(html_content)

    # 使用Playwright渲染
    async with async_playwright() as p:
        browser = await p.chromium.launch()
        # 确保视口大小与模板大小完全一致
        page = await browser.new_page(viewport={"width": 1080, "height": 1440})
        await page.goto(f"file://{os.path.abspath(temp_html)}")

        # 等待所有资源加载完成
        await page.wait_for_load_state("networkidle")

        # 截图并保存
        output_path = os.path.join(OUTPUT_DIR, f"xiaohongshu_frame_{frame_number}.png")
        await page.screenshot(path=output_path, full_page=True)

        await browser.close()

    # 删除临时HTML文件
    os.remove(temp_html)

    return output_path

async def main():
    """主程序"""
    print("开始生成小红书风格绘本图片...")
    print("图片将按1:1比例显示，文字区域已优化")

    # 提取文本内容
    try:
        frame_texts = extract_frame_texts()
        print(f"成功提取到 {len(frame_texts)} 个分镜的文字内容")
    except Exception as e:
        print(f"提取文字内容出错: {e}")
        # 如果提取失败，使用默认文本
        frame_texts = [f"分镜 {i} 的故事内容" for i in range(1, 10)]

    # 获取图片文件列表
    image_dir = "output"
    image_files = []

    for i in range(1, 10):
        image_path = os.path.join(image_dir, f"{i}.png")
        if os.path.exists(image_path):
            image_files.append(image_path)
        else:
            print(f"警告: 找不到图片 {image_path}")

    # 确保有足够的图片文件
    if len(image_files) < 9:
        print(f"警告: 只找到 {len(image_files)} 张图片，少于预期的9张")

    # 处理每个分镜
    tasks = []
    for i, image_path in enumerate(image_files, 1):
        if i <= len(frame_texts):
            tasks.append(generate_image(i, image_path, frame_texts[i-1]))

    # 并行处理所有图片
    generated_images = await asyncio.gather(*tasks)

    print("\n生成完成！")
    print(f"生成了 {len(generated_images)} 张图片，保存在 '{OUTPUT_DIR}' 目录下")
    for img in generated_images:
        print(f" - {img}")

if __name__ == "__main__":
    asyncio.run(main())
