#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
全自动绘本生成脚本测试工具
用于验证各个组件是否正常工作
"""

import os
import sys
import asyncio
from pathlib import Path

def test_file_exists(filepath, description):
    """测试文件是否存在"""
    if os.path.exists(filepath):
        print(f"✅ {description}: {filepath}")
        return True
    else:
        print(f"❌ {description}: {filepath} (文件不存在)")
        return False

def test_directory_exists(dirpath, description):
    """测试目录是否存在"""
    if os.path.exists(dirpath):
        print(f"✅ {description}: {dirpath}")
        return True
    else:
        print(f"⚠️  {description}: {dirpath} (目录不存在，将自动创建)")
        return False

def test_python_import(module_name):
    """测试Python模块是否可以导入"""
    try:
        __import__(module_name)
        print(f"✅ Python模块: {module_name}")
        return True
    except ImportError:
        print(f"❌ Python模块: {module_name} (未安装)")
        return False

def test_huiben_file_format(filepath):
    """测试huiben.md文件格式"""
    if not os.path.exists(filepath):
        print(f"❌ 绘本文件格式检查: {filepath} (文件不存在)")
        return False
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含分镜信息
        frame_count = 0
        for i in range(1, 10):
            if f"{i}." in content:
                frame_count += 1
        
        if frame_count >= 9:
            print(f"✅ 绘本文件格式: 找到 {frame_count} 个分镜")
            return True
        else:
            print(f"⚠️  绘本文件格式: 只找到 {frame_count} 个分镜，建议包含9个分镜")
            return False
            
    except Exception as e:
        print(f"❌ 绘本文件格式检查失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🧪 全自动绘本生成脚本环境检查")
    print("=" * 50)
    
    all_passed = True
    
    # 1. 检查核心脚本文件
    print("\n📄 检查核心脚本文件:")
    core_files = [
        ("auto_generate_huiben.py", "主脚本"),
        ("doubao_simple_test.py", "豆包图片生成脚本"),
        ("remove_watermark_precise.py", "水印去除脚本"),
        ("generate_images.py", "绘本模板生成脚本"),
        ("template.html", "绘本HTML模板")
    ]
    
    for filepath, description in core_files:
        if not test_file_exists(filepath, description):
            all_passed = False
    
    # 2. 检查配置文件
    print("\n📋 检查配置文件:")
    config_files = [
        ("huiben.md", "绘本内容文件"),
        ("mask/mask.jpg", "水印蒙版文件")
    ]
    
    for filepath, description in config_files:
        if not test_file_exists(filepath, description):
            all_passed = False
    
    # 3. 检查目录结构
    print("\n📁 检查目录结构:")
    directories = [
        ("test_images", "豆包图片输出目录"),
        ("output", "水印去除输出目录"),
        ("output_images", "最终绘本输出目录"),
        ("mask", "水印蒙版目录")
    ]
    
    for dirpath, description in directories:
        test_directory_exists(dirpath, description)
    
    # 4. 检查Python依赖
    print("\n🐍 检查Python依赖:")
    required_modules = [
        "asyncio",
        "subprocess", 
        "pathlib",
        "argparse",
        "datetime",
        "shutil",
        "os",
        "sys"
    ]
    
    for module in required_modules:
        if not test_python_import(module):
            all_passed = False
    
    # 5. 检查可选依赖
    print("\n📦 检查可选依赖:")
    optional_modules = [
        ("playwright", "网页自动化 (必需)"),
        ("jinja2", "模板渲染 (必需)"),
        ("pyperclip", "剪贴板操作 (可选)")
    ]
    
    for module, description in optional_modules:
        if test_python_import(module):
            print(f"    {description}")
        else:
            print(f"    {description} - 请运行: pip install {module}")
            if module in ["playwright", "jinja2"]:
                all_passed = False
    
    # 6. 检查绘本文件格式
    print("\n📖 检查绘本文件格式:")
    test_huiben_file_format("huiben.md")
    
    # 7. 检查启动脚本
    print("\n🚀 检查启动脚本:")
    startup_scripts = [
        ("run_auto_huiben.sh", "Linux/macOS启动脚本"),
        ("run_auto_huiben.bat", "Windows启动脚本")
    ]
    
    for filepath, description in startup_scripts:
        test_file_exists(filepath, description)
    
    # 8. 特殊检查
    print("\n🔧 特殊检查:")
    
    # 检查Playwright浏览器
    try:
        from playwright.async_api import async_playwright
        async with async_playwright() as p:
            try:
                browser = await p.chromium.launch(headless=True)
                await browser.close()
                print("✅ Playwright Chrome浏览器: 可用")
            except Exception as e:
                print(f"❌ Playwright Chrome浏览器: 不可用 ({e})")
                print("    请运行: playwright install chrome")
                all_passed = False
    except ImportError:
        print("❌ Playwright: 未安装")
        all_passed = False
    
    # 总结
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有检查通过！环境配置正确，可以开始使用全自动绘本生成脚本")
        print("\n使用方法:")
        print("  Linux/macOS: ./run_auto_huiben.sh")
        print("  Windows:     run_auto_huiben.bat")
        print("  直接运行:    python auto_generate_huiben.py")
    else:
        print("❌ 环境检查发现问题，请根据上述提示解决后重试")
        print("\n常见解决方案:")
        print("  1. 安装依赖: pip install playwright jinja2")
        print("  2. 安装浏览器: playwright install chrome")
        print("  3. 检查文件路径和权限")
    
    print("=" * 50)
    return all_passed

if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
