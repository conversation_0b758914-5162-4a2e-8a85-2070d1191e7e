#!/usr/bin/env python3
"""
测试新的图像生成完成检测逻辑
验证break按钮的!hidden属性检测是否正确实现
"""

import os
import re

def test_generation_detection_logic():
    """测试图像生成完成检测逻辑"""
    print("🔍 测试图像生成完成检测逻辑...")
    
    try:
        with open("doubao_simple_test.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查关键检测点
        detection_checks = [
            ("break按钮选择器", r'data-testid="chat_input_local_break_button"'),
            ("!hidden属性检测", r"classList\.includes\('!hidden'\)"),
            ("生成完成状态", r"generation_complete"),
            ("生成中状态", r"generating"),
            ("按钮class日志", r"Break按钮class")
        ]
        
        all_passed = True
        for check_name, pattern in detection_checks:
            if re.search(pattern, content):
                print(f"✅ {check_name}: 已实现")
            else:
                print(f"❌ {check_name}: 未找到")
                all_passed = False
        
        # 检查是否移除了旧的检测逻辑
        old_logic_patterns = [
            (r"img\[src\*='doubao'\]", "旧的图像选择器"),
            (r"naturalWidth", "图像尺寸检测"),
            (r"generated_image", "旧的生成图像类型")
        ]
        
        print("\n🗑️ 检查旧检测逻辑是否已移除...")
        for pattern, description in old_logic_patterns:
            if re.search(pattern, content):
                print(f"⚠️ {description}: 仍然存在（可能需要进一步清理）")
            else:
                print(f"✅ {description}: 已移除")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检测逻辑测试失败: {e}")
        return False

def test_javascript_syntax():
    """测试JavaScript代码语法"""
    print("\n🔧 测试JavaScript代码语法...")
    
    try:
        with open("doubao_simple_test.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 提取JavaScript代码块
        js_pattern = r'await page\.evaluate\("""(.*?)"""\)'
        js_blocks = re.findall(js_pattern, content, re.DOTALL)
        
        if not js_blocks:
            print("❌ 未找到JavaScript代码块")
            return False
        
        print(f"📝 找到 {len(js_blocks)} 个JavaScript代码块")
        
        # 检查关键JavaScript语法
        for i, js_code in enumerate(js_blocks):
            if 'chat_input_local_break_button' in js_code:
                print(f"✅ 代码块 {i+1}: 包含break按钮检测逻辑")
                
                # 检查语法要点
                syntax_checks = [
                    ("querySelector", "querySelector" in js_code),
                    ("className检查", "className" in js_code),
                    ("includes方法", "includes" in js_code),
                    ("返回对象", "return {" in js_code),
                    ("console.log", "console.log" in js_code)
                ]
                
                for check_name, check_result in syntax_checks:
                    if check_result:
                        print(f"  ✅ {check_name}: 正确")
                    else:
                        print(f"  ❌ {check_name}: 缺失")
                
                return True
        
        print("❌ 未找到包含break按钮检测的JavaScript代码")
        return False
        
    except Exception as e:
        print(f"❌ JavaScript语法测试失败: {e}")
        return False

def test_detection_flow():
    """测试检测流程逻辑"""
    print("\n🔄 测试检测流程逻辑...")
    
    try:
        with open("doubao_simple_test.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查流程关键点
        flow_checks = [
            ("等待循环", r"for i in range\(max_wait_time"),
            ("检测条件", r"if not image_generated:"),
            ("生成完成处理", r"if generation_check_result.*found.*False"),
            ("继续下载", r"continue"),
            ("下载逻辑", r"开始使用新的下载逻辑")
        ]
        
        all_passed = True
        for check_name, pattern in flow_checks:
            if re.search(pattern, content):
                print(f"✅ {check_name}: 流程正确")
            else:
                print(f"❌ {check_name}: 流程缺失")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检测流程测试失败: {e}")
        return False

def create_test_html():
    """创建测试HTML文件用于验证检测逻辑"""
    print("\n📄 创建测试HTML文件...")
    
    html_content = '''<!DOCTYPE html>
<html>
<head>
    <title>Break Button Detection Test</title>
    <style>
        .hidden { display: none; }
        .!hidden { display: block; }
        button { padding: 10px; margin: 10px; }
    </style>
</head>
<body>
    <h1>Break Button Detection Test</h1>
    
    <!-- 测试场景1: 按钮包含!hidden类（生成完成） -->
    <div>
        <h2>场景1: 生成完成（包含!hidden）</h2>
        <button data-testid="chat_input_local_break_button" class="btn !hidden">
            Break Button (Complete)
        </button>
    </div>
    
    <!-- 测试场景2: 按钮不包含!hidden类（生成中） -->
    <div>
        <h2>场景2: 生成中（不包含!hidden）</h2>
        <button data-testid="chat_input_local_break_button" class="btn hidden">
            Break Button (Generating)
        </button>
    </div>
    
    <!-- 测试场景3: 按钮不存在 -->
    <div>
        <h2>场景3: 按钮不存在</h2>
        <p>没有break按钮</p>
    </div>
    
    <script>
        // 测试检测逻辑
        function testDetection() {
            const breakButton = document.querySelector('[data-testid="chat_input_local_break_button"]');
            if (!breakButton) {
                console.log("未找到break按钮");
                return { found: false, status: "no_break_button" };
            }

            const classList = breakButton.className || '';
            const hasNotHidden = classList.includes('!hidden');
            
            console.log("Break按钮class:", classList);
            console.log("包含!hidden:", hasNotHidden);

            if (hasNotHidden) {
                console.log("检测到图像生成完成（break按钮包含!hidden）");
                return { 
                    found: true, 
                    status: "generation_complete",
                    buttonClass: classList
                };
            } else {
                console.log("图像仍在生成中（break按钮不包含!hidden）");
                return { 
                    found: false, 
                    status: "generating",
                    buttonClass: classList
                };
            }
        }
        
        // 页面加载后运行测试
        window.onload = function() {
            console.log("开始测试检测逻辑...");
            const result = testDetection();
            console.log("检测结果:", result);
        };
    </script>
</body>
</html>'''
    
    try:
        with open("test_break_button.html", "w", encoding="utf-8") as f:
            f.write(html_content)
        print("✅ 测试HTML文件已创建: test_break_button.html")
        print("💡 可以在浏览器中打开此文件测试检测逻辑")
        return True
    except Exception as e:
        print(f"❌ 创建测试HTML文件失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试新的图像生成完成检测逻辑...")
    
    tests = [
        ("检测逻辑实现", test_generation_detection_logic),
        ("JavaScript语法", test_javascript_syntax),
        ("检测流程", test_detection_flow),
        ("创建测试文件", create_test_html)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n{'='*50}")
    print(f"测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！新的检测逻辑已正确实现！")
        print("\n📋 检测逻辑总结:")
        print("1. ✅ 使用data-testid='chat_input_local_break_button'选择器")
        print("2. ✅ 检查按钮class是否包含'!hidden'属性")
        print("3. ✅ 包含'!hidden'表示生成完成")
        print("4. ✅ 不包含'!hidden'表示仍在生成")
        print("5. ✅ 完整的错误处理和日志记录")
        print("\n🚀 现在可以运行脚本测试新的检测逻辑")
        return True
    else:
        print("❌ 部分测试失败，请检查实现")
        return False

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
